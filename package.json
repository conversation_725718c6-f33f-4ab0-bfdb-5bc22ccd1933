{"name": "dependent-exe", "version": "1.0.0", "description": "A TypeScript npm package built with tsup", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["typescript", "npm", "package"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.1.0", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.3"}}