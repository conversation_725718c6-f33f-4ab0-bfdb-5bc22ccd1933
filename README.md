# dependent-exe

A TypeScript npm package built with tsup.

## Installation

```bash
npm install dependent-exe
```

## Usage

```typescript
import { hello, add } from 'dependent-exe'

// or
import pkg from 'dependent-exe'

console.log(hello('TypeScript')) // Hello, TypeScript!
console.log(add(2, 3)) // 5
```

## Development

### Install dependencies

```bash
npm install
```

### Build

```bash
npm run build
```

### Development mode (watch)

```bash
npm run dev
```

### Clean build output

```bash
npm run clean
```

## Scripts

- `npm run build` - Build the package using tsup
- `npm run dev` - Build in watch mode for development
- `npm run clean` - Clean the dist directory
- `npm run prepublishOnly` - Automatically build before publishing

## License

ISC
