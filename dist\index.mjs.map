{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Main entry point for the dependent-exe package\n */\n\nexport function hello(name: string = 'World'): string {\n  return `Hello, ${name}!`\n}\n\nexport function add(a: number, b: number): number {\n  return a + b\n}\n\n// Default export\nexport default {\n  hello,\n  add,\n}\n"], "mappings": ";AAIO,SAAS,MAAM,OAAe,SAAiB;AACpD,SAAO,UAAU,IAAI;AACvB;AAEO,SAAS,IAAI,GAAW,GAAmB;AAChD,SAAO,IAAI;AACb;AAGA,IAAO,gBAAQ;AAAA,EACb;AAAA,EACA;AACF;", "names": []}