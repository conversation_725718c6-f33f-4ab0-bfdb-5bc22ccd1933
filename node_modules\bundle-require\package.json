{"name": "bundle-require", "version": "5.1.0", "description": "bundle and require a file", "publishConfig": {"access": "public"}, "files": ["dist"], "type": "module", "main": "./dist/index.js", "exports": {".": {"import": "./dist/index.js", "default": "./dist/index.cjs"}}, "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts-resolve --target node12.20.0", "test": "npm run build && vitest run", "prepublishOnly": "npm run build"}, "license": "MIT", "devDependencies": {"@egoist/prettier-config": "1.0.0", "@types/node": "18.11.18", "esbuild": "0.18.20", "prettier": "2.8.3", "tsup": "6.5.0", "typescript": "4.9.5", "vitest": "0.28.3"}, "dependencies": {"load-tsconfig": "^0.2.3"}, "peerDependencies": {"esbuild": ">=0.18"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}